[{"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js": "1", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js": "2", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js": "3", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js": "4", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js": "5", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js": "6", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js": "7", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js": "8", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js": "9", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js": "10", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js": "11", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js": "13", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js": "14", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js": "15", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js": "16", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js": "17", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js": "18", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\EnterpriseNavbar.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\EnterprisePerformanceDashboard.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\EnterprisePWAInstaller.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js": "26", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js": "30", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js": "31", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js": "32", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js": "33", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js": "34", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js": "35", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js": "36", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js": "37", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js": "38", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js": "39", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js": "40", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js": "41", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js": "42", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js": "43", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\accessibilityTesting.js": "44", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoManager.js": "45", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredDataManager.js": "46"}, {"size": 6440, "mtime": 1750136629000, "results": "47", "hashOfConfig": "48"}, {"size": 4750, "mtime": 1750228680000, "results": "49", "hashOfConfig": "48"}, {"size": 6263, "mtime": 1750228714000, "results": "50", "hashOfConfig": "48"}, {"size": 4150, "mtime": 1750227113000, "results": "51", "hashOfConfig": "48"}, {"size": 2352, "mtime": 1750227130000, "results": "52", "hashOfConfig": "48"}, {"size": 7630, "mtime": 1750227164000, "results": "53", "hashOfConfig": "48"}, {"size": 2591, "mtime": 1750223066000, "results": "54", "hashOfConfig": "48"}, {"size": 1555, "mtime": 1750137055000, "results": "55", "hashOfConfig": "48"}, {"size": 558, "mtime": 1750137055000, "results": "56", "hashOfConfig": "48"}, {"size": 755, "mtime": 1750137055000, "results": "57", "hashOfConfig": "48"}, {"size": 4688, "mtime": 1752410773610, "results": "58", "hashOfConfig": "48"}, {"size": 996, "mtime": 1752789347014, "results": "59", "hashOfConfig": "60"}, {"size": 3811, "mtime": 1752534114385, "results": "61", "hashOfConfig": "48"}, {"size": 2920, "mtime": 1750137055000, "results": "62", "hashOfConfig": "48"}, {"size": 2000, "mtime": 1752534592254, "results": "63", "hashOfConfig": "48"}, {"size": 2319, "mtime": 1752996879020, "results": "64", "hashOfConfig": "48"}, {"size": 6153, "mtime": 1752996810882, "results": "65", "hashOfConfig": "48"}, {"size": 1306, "mtime": 1750137055000, "results": "66", "hashOfConfig": "48"}, {"size": 5482, "mtime": 1752761132661, "results": "67", "hashOfConfig": "60"}, {"size": 11107, "mtime": 1752584749354, "results": "68", "hashOfConfig": "60"}, {"size": 9367, "mtime": 1752584749214, "results": "69", "hashOfConfig": "60"}, {"size": 19030, "mtime": 1752828902760, "results": "70", "hashOfConfig": "60"}, {"size": 6089, "mtime": 1752584748691, "results": "71", "hashOfConfig": "60"}, {"size": 47463, "mtime": 1753001768434, "results": "72", "hashOfConfig": "60"}, {"size": 34116, "mtime": 1753001905457, "results": "73", "hashOfConfig": "60"}, {"size": 13396, "mtime": 1752509953353, "results": "74", "hashOfConfig": "48"}, {"size": 8239, "mtime": 1752584747158, "results": "75", "hashOfConfig": "60"}, {"size": 1853, "mtime": 1752846711139, "results": "76", "hashOfConfig": "48"}, {"size": 1189, "mtime": 1752229991826, "results": "77", "hashOfConfig": "48"}, {"size": 17127, "mtime": 1750189264000, "results": "78", "hashOfConfig": "48"}, {"size": 1147, "mtime": 1750137055000, "results": "79", "hashOfConfig": "48"}, {"size": 1901, "mtime": 1750137056000, "results": "80", "hashOfConfig": "48"}, {"size": 1700, "mtime": 1752558963287, "results": "81", "hashOfConfig": "48"}, {"size": 11592, "mtime": 1752669072282, "results": "82", "hashOfConfig": "48"}, {"size": 18399, "mtime": 1752417314631, "results": "83", "hashOfConfig": "48"}, {"size": 11007, "mtime": 1752789380820, "results": "84", "hashOfConfig": "48"}, {"size": 2033, "mtime": 1750137056000, "results": "85", "hashOfConfig": "48"}, {"size": 15647, "mtime": 1752532937438, "results": "86", "hashOfConfig": "48"}, {"size": 19639, "mtime": 1752789179363, "results": "87", "hashOfConfig": "48"}, {"size": 10182, "mtime": 1750224902000, "results": "88", "hashOfConfig": "48"}, {"size": 2224, "mtime": 1752757322917, "results": "89", "hashOfConfig": "48"}, {"size": 4983, "mtime": 1752413498649, "results": "90", "hashOfConfig": "48"}, {"size": 1951, "mtime": 1752826470161, "results": "91", "hashOfConfig": "48"}, {"size": 10489, "mtime": 1752996051268, "results": "92", "hashOfConfig": "48"}, {"size": 10890, "mtime": 1752996663790, "results": "93", "hashOfConfig": "48"}, {"size": 11204, "mtime": 1752996721307, "results": "94", "hashOfConfig": "48"}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lcw2q5", {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19x6zoz", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\EnterpriseNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\EnterprisePerformanceDashboard.tsx", ["233", "234"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\EnterprisePWAInstaller.tsx", ["235"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\accessibilityTesting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredDataManager.js", [], [], {"ruleId": "236", "severity": 1, "message": "237", "line": 593, "column": 6, "nodeType": "238", "endLine": 593, "endColumn": 8, "suggestions": "239"}, {"ruleId": "236", "severity": 1, "message": "240", "line": 608, "column": 6, "nodeType": "238", "endLine": 608, "endColumn": 8, "suggestions": "241"}, {"ruleId": "236", "severity": 1, "message": "242", "line": 298, "column": 6, "nodeType": "238", "endLine": 298, "endColumn": 8, "suggestions": "243"}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'startPolling' and 'updateMetrics'. Either include them or remove the dependency array.", "ArrayExpression", ["244"], "React Hook useCallback has missing dependencies: 'collectMetrics' and 'startPolling'. Either include them or remove the dependency array.", ["245"], "React Hook useCallback has missing dependencies: 'shouldShowInstallPrompt' and 'trackInstallation'. Either include them or remove the dependency array.", ["246"], {"desc": "247", "fix": "248"}, {"desc": "249", "fix": "250"}, {"desc": "251", "fix": "252"}, "Update the dependencies array to be: [startPolling, updateMetrics]", {"range": "253", "text": "254"}, "Update the dependencies array to be: [collectMetrics, startPolling]", {"range": "255", "text": "256"}, "Update the dependencies array to be: [shouldShowInstallPrompt, trackInstallation]", {"range": "257", "text": "258"}, [17141, 17143], "[startPolling, updateMetrics]", [17485, 17487], "[collectMetrics, startPolling]", [7830, 7832], "[shouldShowInstallPrompt, trackInstallation]"]