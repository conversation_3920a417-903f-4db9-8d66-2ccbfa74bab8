'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useReducedMotion } from '@/hooks/useAdvancedAnimations';
import PerformantWhatsApp from '@/components/PerformantWhatsApp';
import { HeroTitle, SubTitle, StatNumber, StatLabel, Badge } from '@/components/ui/UnifiedTypography';
import { SecondaryButton } from '@/components/ui/UnifiedButton';

// Removed throttle function - no longer needed for better scroll performance

const MinimalistHero = React.memo(function MinimalistHero() {
  const [isLoaded, setIsLoaded] = useState(false);
  const heroRef = useRef(null);

  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{
        paddingTop: "120px",
        backgroundColor: "#FCF6EE"
      }}
    >
      {/* Optimized static background for better scroll performance */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: "url('/images/background/bali-hero.webp')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundAttachment: "scroll",
          backgroundRepeat: "no-repeat"
        }}
      />

      {/* Static overlay gradient for better performance */}
      <div
        className="absolute inset-0 z-10"
        style={{
          background: `
            linear-gradient(
              180deg,
              rgba(252,246,238,0.3) 0%,
              rgba(255,255,255,0.6) 70%,
              rgba(255,255,255,0.85) 100%
            )
          `
        }}
      />

      {/* Subtle texture overlay - reduced opacity for quiet luxury */}
      <div
        className="absolute inset-0 z-10 opacity-10"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          mixBlendMode: 'multiply'
        }}
      />

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes staggeredFadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .fade-in-up {
          animation: fadeInUp 0.8s ease-out forwards;
        }
        
        .staggered-fade {
          animation: staggeredFadeIn 1s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
        }

        /* Custom selection color */
        ::selection {
          background-color: rgb(139, 115, 85);
          color: white;
        }
        
        ::-moz-selection {
          background-color: rgb(139, 115, 85);
          color: white;
        }
      `}</style>
      {/* Główna zawartość wycentrowana */}
      <div className={`relative z-10 text-center max-w-5xl mx-auto px-6 transition-all duration-1000 ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}>
        
        {/* Badge - RETREATY JOGI (Enterprise refined) */}
        <div className={`inline-block mb-6 transition-all duration-700 delay-200 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}>
          <Badge variant="outline" className="text-[11px] tracking-[3.5px]">
            RETREATY JOGI • BALI & SRI LANKA
          </Badge>
        </div>

        {/* Tytuł BAKASANA - Enterprise luxury */}
        <div className={`transition-all duration-1000 delay-300 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <HeroTitle>BAKASANA</HeroTitle>
        </div>

        {/* Podtytuł - Refined */}
        <div className={`transition-all duration-1000 delay-400 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <SubTitle>~ jóga jest drogą ciszy ~</SubTitle>
        </div>

        {/* Opis główny - Fluid typography */}
        <p className={`text-[clamp(1rem,2.5vw,1.125rem)] text-charcoal-light max-w-[620px] mx-auto leading-[1.75] font-normal mb-16 transition-all duration-300 delay-500 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          Odkryj transformującą moc jogi w duchowych sercach Azji. Dołącz do naszej autentycznej podróży przez terasy ryżowe Ubud, świątynie Bali i tajemnicze krajobrazy Sri Lanki.
        </p>

        {/* Statystyki - Prawdziwe i autentyczne z mikrodata */}
        <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-20 max-w-4xl mx-auto mb-16 transition-all duration-1000 delay-600 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <div className="text-center" itemScope itemType="https://schema.org/EducationalOccupationalCredential">
            <StatNumber itemProp="credentialLevel">200h</StatNumber>
            <StatLabel itemProp="name">CERTYFIKACJA YTT</StatLabel>
          </div>
          <div className="text-center">
            <StatNumber>7</StatNumber>
            <StatLabel>LAT DOŚWIADCZENIA</StatLabel>
          </div>
          <div className="text-center">
            <StatNumber>150+</StatNumber>
            <StatLabel>ZADOWOLONYCH UCZESTNIKÓW</StatLabel>
          </div>
          <div className="text-center" itemScope itemType="https://schema.org/AggregateRating">
            <StatNumber itemProp="ratingValue">4.9</StatNumber>
            <StatLabel>ŚREDNIA OCEN</StatLabel>
            <meta itemProp="bestRating" content="5" />
            <meta itemProp="reviewCount" content="150" />
          </div>
        </div>

        {/* Przyciski CTA - Refined timing */}
        <div className={`flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center transition-all duration-300 delay-700 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <SecondaryButton
            as={Link}
            href="/program"
            size="lg"
            className="transition-all duration-300 hover:transform hover:-translate-y-0.5"
          >
            PRZEGLĄD HARMONOGRAMU
          </SecondaryButton>

          <PerformantWhatsApp
            size="md"
            variant="button"
            className="inline-flex items-center font-light tracking-[2px] transition-all duration-300 hover:transform hover:-translate-y-0.5 focus:outline-none focus:opacity-70 px-12 py-4 text-sm"
          />

          <SecondaryButton
            as={Link}
            href="/rezerwacja"
            size="lg"
            className="transition-all duration-300 hover:transform hover:-translate-y-0.5"
          >
            REZERWUJ KONSULTACJĘ
          </SecondaryButton>
        </div>
      </div>

      {/* Formularz boczny - Quiet luxury refinement */}
      <div className={`hidden xl:block absolute right-8 top-1/2 transform -translate-y-1/2 z-10 bg-sanctuary/95 backdrop-blur-sm p-8 max-w-sm w-80 transition-all duration-300 delay-800 border border-enterprise-brown/10 ${
        isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'
      }`}
      style={{
        boxShadow: '0 8px 32px rgba(139, 115, 85, 0.08)'
      }}>
        <h3 className="text-xl font-cormorant text-charcoal mb-2">Gotowa na transformację?</h3>
        <p className="text-sm text-charcoal-light mb-6">Rozpocznij swoją duchową podróż</p>
        <div className="space-y-4">
          <input
            type="email"
            placeholder="Twój email"
            className="w-full px-0 py-3 border-0 border-b border-stone-light text-sm focus:outline-none focus:border-enterprise-brown transition-colors duration-300 bg-transparent"
            disabled
          />
          <Link
            href="/kontakt"
            className="w-full px-6 py-3 bg-enterprise-brown text-sanctuary font-light tracking-[2px] transition-all duration-300 hover:bg-terra hover:-translate-y-0.5 flex items-center justify-center focus:outline-none focus:opacity-80"
            style={{ fontSize: '13px' }}
          >
            KONTAKT →
          </Link>
        </div>
      </div>
    </section>
  );
});

export default MinimalistHero;