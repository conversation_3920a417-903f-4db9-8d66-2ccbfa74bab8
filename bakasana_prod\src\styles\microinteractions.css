/* =============================================
   🎯 BAKASANA - AWARD-WINNING MICROINTERACTIONS
   Inspired by Apple.com, Stripe.com, Linear.app
   ============================================= */

/* ===== UNIFIED ANIMATION SYSTEM ===== */
:root {
  /* Standardized timing functions */
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Unified duration system */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  /* Transform origins */
  --center-origin: center center;
  --top-origin: center top;
  --bottom-origin: center bottom;
}

/* ===== BUTTON MICROINTERACTIONS ===== */
.btn-primary {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all var(--duration-normal) var(--ease-smooth);
  will-change: transform, box-shadow, background-color;
  contain: layout style paint;
  
  /* Unified hover effect */
  &:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 8px 20px rgba(184, 147, 92, 0.2),
      0 4px 10px rgba(184, 147, 92, 0.1);
    background: linear-gradient(135deg, #C4996B 0%, #B8935C 100%);
  }
  
  /* Active state with spring back */
  &:active {
    transform: translateY(0px) scale(0.98);
    transition-duration: var(--duration-fast);
  }
  
  /* Magnetic effect on hover */
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), 
                               rgba(255, 255, 255, 0.1) 0%, 
                               transparent 70%);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-smooth);
    pointer-events: none;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  /* Ripple effect */
  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: scale(0);
    pointer-events: none;
    transition: transform var(--entrance-duration) var(--spring-elastic);
  }
  
  &:focus::after {
    width: 100%;
    height: 100%;
    transform: scale(1);
  }
}

/* ===== CARD MICROINTERACTIONS ===== */
.card-interactive {
  position: relative;
  transform: translateZ(0);
  transition: all var(--duration-normal) var(--ease-smooth);
  will-change: transform, box-shadow;
  contain: layout style paint;
  
  /* Unified card hover */
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 12px 24px rgba(42, 39, 36, 0.08),
      0 6px 12px rgba(42, 39, 36, 0.06);
  }
  
  /* Simplified 2D tilt effect - no 3D transforms */
  &.tilt-active {
    transition: transform var(--micro-duration) var(--spring-smooth);
  }
  
  /* Glowing border on focus */
  &:focus-within {
    outline: none;
    box-shadow: 
      0 0 0 2px rgba(184, 147, 92, 0.2),
      0 0 0 4px rgba(184, 147, 92, 0.1),
      0 20px 40px rgba(42, 39, 36, 0.08);
  }
}

/* ===== LINK MICROINTERACTIONS ===== */
.link-enhanced {
  position: relative;
  color: var(--temple-gold);
  text-decoration: none;
  overflow: hidden;
  transition: color var(--standard-duration) var(--spring-smooth);
  
  /* Animated underline */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--temple-gold), var(--golden-amber));
    transition: width var(--standard-duration) var(--spring-smooth);
  }
  
  &:hover::after {
    width: 100%;
  }
  
  /* Shine effect */
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 70%
    );
    transform: translateX(-100%);
    transition: transform var(--entrance-duration) var(--spring-smooth);
  }
  
  &:hover::before {
    transform: translateX(100%);
  }
}

/* ===== FORM MICROINTERACTIONS ===== */
.form-field {
  position: relative;
  
  input, textarea {
    transition: all var(--standard-duration) var(--spring-smooth);
    will-change: border-color, box-shadow, background-color;
    
    &:focus {
      outline: none;
      border-color: var(--temple-gold);
      box-shadow: 
        0 0 0 3px rgba(184, 147, 92, 0.1),
        0 4px 12px rgba(184, 147, 92, 0.08);
      background-color: rgba(253, 252, 248, 0.8);
    }
    
    &:invalid {
      border-color: #e74c3c;
      box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    }
  }
  
  /* Floating labels */
  label {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--stone);
    pointer-events: none;
    transition: all var(--standard-duration) var(--spring-smooth);
    background: linear-gradient(to bottom, 
                               transparent 0%, 
                               transparent 40%, 
                               var(--sanctuary) 50%, 
                               var(--sanctuary) 100%);
    padding: 0 4px;
  }
  
  input:focus + label,
  input:not(:placeholder-shown) + label {
    top: 0;
    transform: translateY(-50%);
    font-size: 12px;
    color: var(--temple-gold);
  }
}

/* ===== SCROLL REVEAL ANIMATIONS ===== */
.reveal-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--entrance-duration) var(--spring-smooth);
  will-change: opacity, transform;
  
  &.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}

.reveal-stagger {
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--entrance-duration) var(--spring-smooth);
  will-change: opacity, transform;
  
  &.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered delays for multiple elements */
.reveal-stagger:nth-child(1) { transition-delay: 0s; }
.reveal-stagger:nth-child(2) { transition-delay: 0.1s; }
.reveal-stagger:nth-child(3) { transition-delay: 0.2s; }
.reveal-stagger:nth-child(4) { transition-delay: 0.3s; }
.reveal-stagger:nth-child(5) { transition-delay: 0.4s; }

/* ===== MAGNETIC CURSOR EFFECTS ===== */
.magnetic-element {
  position: relative;
  cursor: none;
  transition: transform var(--micro-duration) var(--spring-smooth);
  will-change: transform;
}

.cursor-follower {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--temple-gold);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform var(--micro-duration) var(--spring-smooth);
  will-change: transform;
  
  &.expanded {
    transform: scale(2);
    background: rgba(184, 147, 92, 0.3);
  }
}

/* ===== GLASSMORPHISM EFFECTS ===== */
.glass-card {
  background: rgba(253, 252, 248, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all var(--standard-duration) var(--spring-smooth);
  will-change: background, backdrop-filter;
  
  &:hover {
    background: rgba(253, 252, 248, 0.9);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
  }
}

/* ===== LOADING ANIMATIONS ===== */
.loading-shimmer {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    90deg,
    var(--whisper) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    var(--whisper) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ===== PARALLAX SCROLL EFFECTS ===== */
.parallax-container {
  overflow: hidden;
  position: relative;
}

.parallax-layer {
  position: absolute;
  inset: 0;
  will-change: transform;
  transition: transform var(--micro-duration) linear;
}

.parallax-slow { transform: translateY(var(--parallax-slow, 0)); }
.parallax-medium { transform: translateY(var(--parallax-medium, 0)); }
.parallax-fast { transform: translateY(var(--parallax-fast, 0)); }

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .parallax-layer {
    transform: none !important;
  }
}

/* ===== FOCUS INDICATORS ===== */
.focus-visible {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
  border-radius: 4px;
}

/* ===== SMOOTH SCROLLING ===== */
html {
  scroll-behavior: smooth;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}