/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx}'],
  theme: {
    extend: {
      colors: {
        // =============================================
        // 🏛️ BAKASANA - REFINED OLD MONEY + WARM MINIMALISM
        // Elegancja + Ciepło + Organiczne elementy
        // =============================================

        // CORE WARM NEUTRALS - Ciepła podstawa
        sanctuary: '#FDFCF8',        // Główne tło - warm cream
        parchment: '#FCF6EE',        // Hero tło - subtle warmth
        linen: '#F9F6F1',            // Subtelne tła - organic linen
        whisper: '#F5F2ED',          // Ultra-subtelne - barely there
        silk: '#F0EDE8',             // Karty - soft silk texture
        
        // WARM CHARCOALS - Ciepłe ciemne tony
        charcoal: '#2A2724',         // Główny tekst - warm dark
        'charcoal-light': '#4A453F', // Lighter text - muted warmth
        ash: '#6B6560',              // Secondary text - warm ash
        sage: '#8B8680',             // Subtle text - warm sage
        stone: '#A8A39E',            // Light text - warm stone
        'stone-light': '#C4BFB8',    // Very light - barely visible
        
        // ENTERPRISE WARM ACCENTS - Ciepłe akcenty
        'enterprise-brown': '#8B7355', // Primary accent - sophisticated
        terra: '#B8935C',            // Hover states - warm terra
        sand: '#D4AF7A',             // Golden highlights - desert sand
        amber: '#E6C18A',            // Light accents - warm amber
        
        // ORGANIC WARM TOUCHES - Naturalne ciepło
        'warm-gold': '#C19B68',      // Organic gold - natural warmth
        'soft-bronze': '#A67C52',    // Bronze touches - earthy
        'clay': '#9B7B5F',           // Clay tones - grounded
        'honey': '#E8D5B7',          // Honey highlights - sweet warmth
        
        // PURE CONTRASTS - Czyste kontrasty
        'pure-white': '#FFFFFF',     // Clean white
        'soft-black': '#1A1816',     // Soft black - not harsh
        
        // TRANSPARENCY SYSTEM - Przezroczystości
        'glass-nav': 'rgba(253, 252, 248, 0.95)',
        'hero-overlay': 'rgba(252, 246, 238, 0.4)',
        'subtle-shadow': 'rgba(26, 24, 22, 0.06)',
        'premium-shadow': 'rgba(139, 115, 85, 0.15)',
        'warm-shadow': 'rgba(184, 147, 92, 0.12)',
        
        // UNIFIED COLOR ALIASES - Ujednolicone nazwy
        'temple-gold': '#B8935C',    // Unified with terra
        golden: '#D4AF7A',           // Alias for sand
        temple: '#2A2724',           // Alias for charcoal
        
        // SYSTEM ALIASES - Systemowe
        primary: '#2A2724',          // charcoal
        secondary: '#6B6560',        // ash
        accent: '#8B7355',           // enterprise-brown
        background: '#FDFCF8',       // sanctuary
        
        // GLASSMORPHISM - Szkło
        'glass-light': 'rgba(255, 255, 255, 0.1)',
        'glass-dark': 'rgba(26, 24, 22, 0.1)',
        'glass-accent': 'rgba(184, 147, 92, 0.1)',
        'glass-warm': 'rgba(193, 155, 104, 0.08)',
      },
      fontFamily: {
        // Duchowa typografia - Oddech w słowach
        'cormorant': ['Cormorant Garamond', 'Didot', 'Bodoni MT', 'Playfair Display', 'serif'],
        'inter': ['Inter', 'Helvetica Neue', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],

        // Zachowane dla kompatybilności
        'playfair': ['var(--font-playfair)', 'Playfair Display', 'serif'],
        'didot': ['Didot', 'Bodoni MT', 'Playfair Display', 'serif'],
        'helvetica': ['Helvetica Neue', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
      },
      fontSize: {
        // OLD MONEY TYPOGRAPHY SCALE
        'hero-massive': ['clamp(100px, 15vw, 200px)', { lineHeight: '0.95', letterSpacing: '0.15em', fontWeight: '300' }],
        'hero-title': ['4rem', { lineHeight: '1.1', letterSpacing: '0.2em', fontWeight: '300' }],
        'display-xl': ['3rem', { lineHeight: '1.1', letterSpacing: '0.05em', fontWeight: '300' }],
        'display': ['2.5rem', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '300' }],
        'heading-lg': ['2rem', { lineHeight: '1.3', letterSpacing: '0.02em', fontWeight: '300' }],
        'heading': ['1.5rem', { lineHeight: '1.3', letterSpacing: '0.02em', fontWeight: '400' }],
        'subtitle': ['1.3125rem', { lineHeight: '1.4', letterSpacing: '0.01em', fontWeight: '300' }], // 21px
        'body-lg': ['1.0625rem', { lineHeight: '1.85', fontWeight: '400' }], // 17px
        'body': ['1rem', { lineHeight: '1.8', fontWeight: '400' }],
        'caption': ['0.875rem', { lineHeight: '1.6', letterSpacing: '0.05em', fontWeight: '400' }],
        'small': ['0.8125rem', { lineHeight: '1.5', letterSpacing: '0.02em', fontWeight: '400' }], // 13px
        'micro': ['0.6875rem', { lineHeight: '1.4', letterSpacing: '0.1em', fontWeight: '500' }], // 11px
      },
      spacing: {
        // UNIFIED SPACING SYSTEM (8px base)
        'xs': '0.5rem',        // 8px
        'sm': '1rem',          // 16px
        'md': '1.5rem',        // 24px
        'lg': '2rem',          // 32px
        'xl': '3rem',          // 48px
        '2xl': '4rem',         // 64px
        '3xl': '5rem',         // 80px
        '4xl': '7.5rem',       // 120px

        // SPECIALIZED SPACING
        'section': '7.5rem',   // 120px - Section spacing
        'container': '8%',     // Luxury side margins
        'breathe': '11.25rem', // 180px - Ultra-premium breathing
        'card': '3rem',        // 48px - Card internal padding
        'hero-padding': '1.5rem', // 24px - Hero section padding
        'nav-height': '5rem',  // 80px - Navigation height
      },
      screens: {
        // BAKASANA ENTERPRISE BREAKPOINTS - Aligned with CSS
        'xs': '480px',           // Small mobile to mobile
        'sm': '768px',           // Mobile to tablet
        'md': '1024px',          // Tablet to desktop
        'lg': '1440px',          // Desktop to large
        'xl': '1920px',          // Large to ultra
      },
      container: {
        center: true,
        padding: '8%',           // 8% marginesy boczne - przestrzeń jako luksus
        screens: {
          'xs': '480px',
          'sm': '768px',
          'md': '1024px',
          'lg': '1440px',
          'xl': '1920px',
        },
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'lotus-pulse': 'lotusPulse 3s ease-in-out infinite',
        'fade-in-up': 'fadeInUp 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards',
        'scale-in': 'scaleIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards',
        'slide-in-right': 'slideInRight 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards',
        'float': 'float 3s ease-in-out infinite',
        'pulse-gentle': 'pulseGentle 2s ease-in-out infinite',
        'shimmer': 'shimmer 2s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(40px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        lotusPulse: {
          '0%, 100%': { opacity: '0.6', transform: 'scale(1)' },
          '50%': { opacity: '1', transform: 'scale(1.05)' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.8)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
        slideInRight: {
          '0%': { opacity: '0', transform: 'translateX(30px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        pulseGentle: {
          '0%, 100%': { opacity: '0.8', transform: 'scale(1)' },
          '50%': { opacity: '1', transform: 'scale(1.02)' },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
      // UNIFIED SHADOW SYSTEM - Ciepłe i organiczne cienie
      boxShadow: {
        // WARM SHADOW SYSTEM - Ciepłe cienie
        'subtle': '0 2px 8px rgba(26, 24, 22, 0.06)',
        'elegant': '0 4px 16px rgba(139, 115, 85, 0.08)',
        'premium-shadow': '0 8px 32px rgba(139, 115, 85, 0.12)',
        'warm-shadow': '0 6px 24px rgba(184, 147, 92, 0.10)',
        'hero': '0 20px 60px rgba(26, 24, 22, 0.08)',
        
        // ORGANIC SHADOWS - Organiczne cienie
        'organic-light': '0 3px 12px rgba(193, 155, 104, 0.06)',
        'organic-medium': '0 6px 20px rgba(193, 155, 104, 0.08)',
        'organic-strong': '0 12px 40px rgba(193, 155, 104, 0.12)',
        
        // GLASS EFFECTS - Efekty szkła
        'glass': '0 8px 32px rgba(255, 255, 255, 0.1)',
        'glass-warm': '0 8px 32px rgba(253, 252, 248, 0.15)',
        
        // INTERACTIVE SHADOWS - Interaktywne cienie
        'hover': '0 8px 25px rgba(139, 115, 85, 0.15)',
        'focus': '0 0 0 3px rgba(139, 115, 85, 0.1)',
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.whatsapp-elegant': {
          backgroundColor: '#8B7355',
          '&:hover': {
            backgroundColor: 'rgba(139, 115, 85, 0.9)',
          },
        },
        '.whatsapp-float': {
          position: 'fixed',
          bottom: '2.5rem',
          right: '2.5rem',
          boxShadow: '0 4px 12px rgba(139, 115, 85, 0.15)',
          zIndex: '998',
          '@media (max-width: 768px)': {
            bottom: '1.5rem',
            right: '1.5rem',
          },
        },
        '.whatsapp-icon': {
          width: '1.25rem',
          height: '1.25rem',
          color: 'white',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}